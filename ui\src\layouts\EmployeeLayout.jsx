import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";
import Footer from "@/components/Footer";
import { useLocation, Outlet, useNavigate } from "react-router-dom";

// menu items icons (lucide-react)
import {
  LayoutDashboard,   // Dashboard
  CalendarPlus,      // Apply Leave
  History,           // Leave History
  CalendarDays,      // Monthly Attendance
  FileText,          // Payslips
  DollarSign,        // Loan Apply
  ListChecks,        // Loan Status
  CreditCard,        // Bank Details
  Upload,            // Upload Documents
  FileSearch,        // View Documents
  UserCircle,        // Profile Overview
  UserCog,            // Edit Profile
  GraduationCap,
  BriefcaseBusiness
} from "lucide-react";

const EmployeeLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    return typeof window !== "undefined" && window.innerWidth >= 1024;
  });

  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("home");

  // Employee menu items
  const menuItems = [
  // Dashboard
  { id: "home", name: "Home", icon: <LayoutDashboard size={18} />, type: "item", path: "/dashboard" },

  // Leave Management
  { id: "leave-management", name: "Leave Management", type: "heading" },
  { id: "leave-management/apply", name: "Leave Management", icon: <CalendarPlus size={18} />, type: "item", path: "/dashboard/leave-management" },
  { id: "leave-management/history", name: "Leave History", icon: <History size={18} />, type: "item", path: "/dashboard/leave-history" },

  // Attendance
  { id: "attendance", name: "Attendance", type: "heading" },
  { id: "attendance", name: "Monthly Attendance", icon: <CalendarDays size={18} />, type: "item", path: "/dashboard/attendance" },

  // Salary / Payroll
  { id: "salary", name: "Salary & Payslips", type: "heading" },
  { id: "salary-and-payslips", name: "Download Payslips", icon: <FileText size={18} />, type: "item", path: "/dashboard/salary-and-payslips" },

  // Loans
  { id: "loans", name: "Loans", type: "heading" },
  { id: "loan-and-advance", name: "Loans & Advances", icon: <DollarSign size={18} />, type: "item", path: "/dashboard/loan-and-advance" },

  // Bank Details
  { id: "bank-details", name: "Bank Details", type: "heading" },
  { id: "bank-details", name: "View / Update Bank Info", icon: <CreditCard size={18} />, type: "item", path: "/dashboard/bank-details" },

  // Documents
  { id: "documents", name: "Documents", type: "heading" },
  { id: "documents", name: "My Documents", icon: <FileSearch size={18} />, type: "item", path: "/dashboard/documents" },

  // Documents
  { id: "acad-and-prof", name: "Academic and Professional", type: "heading" },
  { id: "academic", name: "Academic", icon: <GraduationCap size={18} />, type: "item", path: "/dashboard/academic" },
  { id: "professional", name: "Professional", icon: <BriefcaseBusiness size={18} />, type: "item", path: "/dashboard/professional" },

];


  // Track active tab based on location
  useEffect(() => {
    const matchedItem = menuItems.find(
      (item) => item.type === "item" && location.pathname === item.path
    );
    setActiveTab(matchedItem ? matchedItem.id : "home");
  }, [location.pathname]);

  // Auto-open sidebar on desktop resize
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1024;
      if (isDesktop && !sidebarOpen) {
        setSidebarOpen(true);
      }
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [sidebarOpen]);

  const getActiveTabTitle = () => {
    const item = menuItems.find((item) => item.id === activeTab && item.type === "item");
    return item ? item.name : "Page Not Found";
  };

  return (
    <div className="h-screen w-full flex overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Sidebar Container */}
      <div className="h-full overflow-y-auto z-50">
        <Sidebar
          menuItems={menuItems}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
        />
      </div>

      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden bg-slate-100">
        <Navbar
          getActiveTabTitle={getActiveTabTitle}
          setSidebarOpen={setSidebarOpen}
          sidebarOpen={sidebarOpen}
        />

        <motion.main
          className="flex-1 p-4 m-4 bg-white rounded-lg shadow-lg overflow-y-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Outlet />
        </motion.main>

        <Footer />
      </div>
    </div>
  );
};

export default EmployeeLayout;
