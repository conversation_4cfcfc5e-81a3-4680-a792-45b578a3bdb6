﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Models;
using EMS.Data;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpCTCandPayrollsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpCTCandPayrollsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpCTCandPayrolls
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpCTCandPayroll>>> GetEmpCTCandPayrolls()
        {
            return await _context.EmpCTCandPayrolls.ToListAsync();
        }

        // GET: api/EmpCTCandPayrolls/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpCTCandPayroll>> GetEmpCTCandPayroll(int id)
        {
            var empCTCandPayroll = await _context.EmpCTCandPayrolls.FindAsync(id);

            if (empCTCandPayroll == null)
            {
                return NotFound();
            }

            return empCTCandPayroll;
        }

        // PUT: api/EmpCTCandPayrolls/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpCTCandPayroll(int id, EmpCTCandPayroll empCTCandPayroll)
        {
            if (id != empCTCandPayroll.EmpCtcID)
            {
                return BadRequest();
            }

            _context.Entry(empCTCandPayroll).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpCTCandPayrollExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpCTCandPayrolls
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpCTCandPayroll>> PostEmpCTCandPayroll(EmpCTCandPayroll empCTCandPayroll)
        {
            _context.EmpCTCandPayrolls.Add(empCTCandPayroll);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpCTCandPayroll", new { id = empCTCandPayroll.EmpCtcID }, empCTCandPayroll);
        }

        // DELETE: api/EmpCTCandPayrolls/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpCTCandPayroll(int id)
        {
            var empCTCandPayroll = await _context.EmpCTCandPayrolls.FindAsync(id);
            if (empCTCandPayroll == null)
            {
                return NotFound();
            }

            _context.EmpCTCandPayrolls.Remove(empCTCandPayroll);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpCTCandPayrollExists(int id)
        {
            return _context.EmpCTCandPayrolls.Any(e => e.EmpCtcID == id);
        }
    }
}
