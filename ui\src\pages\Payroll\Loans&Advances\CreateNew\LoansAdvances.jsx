import { API, notification } from '@/services';
import getLoanById from '@/services/LoansAdvances/GetLoansByEmployee';
import useAuthStore from '@/store/authStore';
import React, { useEffect, useState } from 'react';
import { FaMoneyCheckAlt, FaClipboardList, FaCheckCircle, FaHistory } from 'react-icons/fa';

export default function LoanAdvanceManagement() {
  const initialFormState = {
    monthlyInstallment: '',
    amount: '',
    reason: ''
  };
  const [form, setForm] = useState(initialFormState);
  const [request, setRequest] = useState([])
  const user = useAuthStore((state) => state.user);

  const handleSubmit = async () => {

    const period = (form.amount) / (form.monthlyInstallment)
    const payload = {
      EmpID: user?.empId,
      LoanAmount: parseFloat(form.amount),
      Period: period,
      InterestRate: 0,
      MonthlyInstallment: parseFloat(form.monthlyInstallment),
      StartDate: new Date().toISOString(),
      IsApproved: false,
      Description: form.reason
    };

    try {
      const response = await API.post('/LnAs', payload);
      console.log('Response:', response.data);
      notification().success('Loan request submitted successfully!');
      getEmployeeRequest();
      setForm(initialFormState)
    } catch (error) {
      console.error('Submission failed:', error);
      notification().error('There was an error submitting the request.');
    }
  };

  useEffect(() => {
    const getEmployeeRequest = async () => {
      if (!user?.empId) return;
      try {
        const getStatus = await getLoanById.getLoanByEmp(user.empId);
        setRequest(getStatus);
      }
      catch (error) {
        console.error("Failed to get error", error)
      }
    }
    getEmployeeRequest();
  }, [user])


  return (
    <div className="p-6 md:p-10 bg-gradient-to-br from-gray-100 to-white min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">💼 Loans & Advances Management</h1>
      <div className="grid grid-cols-2 gap-7">
        {/* Request Form */}
        <div className="bg-white shadow-lg p-6 rounded-lg  transition hover:shadow-xl">
          <div className="flex items-center gap-2 mb-4">
            <FaMoneyCheckAlt className="text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Request Loan/Advance</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <label className="block">
              <span className="text-gray-700">Amount</span>
              <input
                type="number"
                value={form.amount}
                onChange={(e) => setForm({ ...form, amount: e.target.value })}
                placeholder="Enter amount"
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </label>
            <label className="block">
              <span className="text-gray-700">Monthly Installment</span>
              <input
                type='number'
                value={form.monthlyInstallment}
                onChange={(e) => setForm({ ...form, monthlyInstallment: e.target.value })}
                disabled={!form.amount}
                placeholder='Enter monthly installment'
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500">Period: {(form.amount) / (form.monthlyInstallment)}</p>
            </label>
            <label className="block col-span-2">
              <span className="text-gray-700">Reason</span>
              <textarea
                value={form.reason}
                onChange={(e) => setForm({ ...form, reason: e.target.value })}
                placeholder="Enter reason"
                rows="3"
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </label>
          </div>
          <button
            onClick={handleSubmit}
            className="mt-6 bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded shadow"
          >
            Submit Request
          </button>
        </div>

        {/* Repayment Info */}
        <div className="bg-white shadow-lg p-6 rounded-lg transition hover:shadow-xl">
          <div className="flex items-center gap-2 mb-4">
            <FaHistory className="text-red-600" />
            <h2 className="text-xl font-semibold text-gray-800">Repayment Status</h2>
          </div>
          <p className="text-gray-800 text-md mb-1">
            <strong>Outstanding Balance:</strong> ₹10,000
          </p>
          <p className="text-gray-800 text-md mb-3">
            <strong>Next EMI:</strong> ₹1,000 on <span className="text-blue-600 font-medium">1st Aug 2025</span>
          </p>
          <div>
            <p className="text-gray-700 mb-2 font-semibold">Repayment History:</p>
            <ul className="list-disc pl-5 text-gray-600 text-sm space-y-1">
              <li>1st Jul 2025 - ₹1,000 - <span className="text-green-600">Paid</span></li>
              <li>1st Jun 2025 - ₹1,000 - <span className="text-green-600">Paid</span></li>
            </ul>
          </div>
        </div>
      </div>


      {/* Request Status */}
      {/* Request Status - Multiple Loans */}
      <div className="bg-white shadow-lg p-6 rounded-lg mb-10 transition hover:shadow-xl">
        <div className="flex items-center gap-2 mb-4">
          <FaCheckCircle className="text-purple-600" />
          <h2 className="text-xl font-semibold text-gray-800">Loan Request Status</h2>
        </div>

        {!request || request?.length === 0 ? (
          <p className="text-gray-600">No loan requests found.</p>
        ) : (
          <ul className="space-y-4">
            {request.map((loan) => (
              <li key={loan.LnAID} className="border p-4 rounded-md bg-gray-50">
                <p className="text-md text-gray-800"><strong>Loan Amount:</strong> ₹{loan.LoanAmount}</p>
                <p className="text-md text-gray-800"><strong>Installment:</strong> ₹{loan.MonthlyInstallment}</p>
                <p className="text-md text-gray-800"><strong>Start Date:</strong> {new Date(loan.StartDate).toLocaleDateString()}</p>
                <p className="text-md text-gray-800"><strong>Reason:</strong> {loan.Description}</p>
                <span
                  className={`inline-block mt-2 px-3 py-1 rounded-full text-sm font-semibold ${loan.IsApproved
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                    }`}
                >
                  {loan.IsApproved ? 'Approved' : 'Pending Approval'}
                </span>
              </li>
            ))}
          </ul>
        )}
      </div>



    </div>
  );
}
