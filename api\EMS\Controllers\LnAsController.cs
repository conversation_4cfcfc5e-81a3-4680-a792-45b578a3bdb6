﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Wordprocessing;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LnAsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public LnAsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/LnAs
        /* [HttpGet]
         public async Task<ActionResult<IEnumerable<LnA>>> GetLnAs()
         {
             var currentMonthNumber = DateTime.Now.Month.ToString();
             var data = await (from s in _context.LnAs
                               join e in _context.Employees on s.EmpID equals e.EmpID
                               join a in _context.LnABalances on s.EmpID equals a.EmpID where a.Month == currentMonthNumber
                               select new
                               {
                                   s.EmpID,
                                   s.LoanAmount,
                                   s.MonthlyInstallment,
                                   s.StartDate,
                                   s.InterestRate,
                                   s.Description,
                                   s.IsApproved,
                                   a.ClosingBalance,
                                   EmployeeName = e.FirstName + " " + e.LastName,
                               }).ToListAsync();
             return Ok(data);
         }*/

        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetLnAs()
        {
            var currentMonthNumber = DateTime.Now.Month.ToString();

            var data = await (from s in _context.LnAs
                              join e in _context.Employees on s.EmpID equals e.EmpID
                              join a in _context.LnABalances
                                  on new { s.EmpID, Month = currentMonthNumber }
                                  equals new { a.EmpID, a.Month }
                                  into balances // left join group
                              from a in balances.DefaultIfEmpty() // flatten
                              select new
                              {
                                  s.LnAID,
                                  s.EmpID,
                                  s.LoanAmount,
                                  s.MonthlyInstallment,
                                  s.StartDate,
                                  s.InterestRate,
                                  s.Period,
                                  s.Description,
                                  s.IsApproved,
                                  ClosingBalance = a != null ? a.ClosingBalance : (double?)null, // handle missing
                                  EmployeeName = e.FirstName + " " + e.LastName,
                              }).ToListAsync();

            return Ok(data);
        }


        // GET: api/LnAs/5
        [HttpGet("{id}")]
        public async Task<ActionResult<LnA>> GetLnA(int id)
        {
            var lnA = await _context.LnAs.FindAsync(id);

            if (lnA == null)
            {
                return NotFound();
            }

            return lnA;
        }

        [HttpGet("ByEmployee")]
        public async Task<ActionResult<IEnumerable<LnA>>> GetLoansByEmployee(int empId)
        {
           return await _context.LnAs.Where(s=>s.EmpID == empId).ToListAsync();
        }

        // PUT: api/LnAs/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutLnA(int id, LnA lnA)
        {
            if (id != lnA.LnAID)
            {
                return BadRequest();
            }

            _context.Entry(lnA).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LnAExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/LnAs
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<LnA>> PostLnA(LnA lnA)
        {
            _context.LnAs.Add(lnA);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetLnA", new { id = lnA.LnAID }, lnA);
        }

        // DELETE: api/LnAs/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLnA(int id)
        {
            var lnA = await _context.LnAs.FindAsync(id);
            if (lnA == null)
            {
                return NotFound();
            }

            _context.LnAs.Remove(lnA);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool LnAExists(int id)
        {
            return _context.LnAs.Any(e => e.LnAID == id);
        }
    }
}
