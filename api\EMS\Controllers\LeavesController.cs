﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;
using System.Security.Claims;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LeavesController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public LeavesController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/Leaves
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Leave>>> GetLeaves()
        {
            return await _context.Leaves.ToListAsync();
        }

        // GET: api/Leaves/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Leave>> GetLeave(int id)
        {
            var leave = await _context.Leaves.FindAsync(id);

            if (leave == null)
            {
                return NotFound();
            }

            return leave;
        }

        // PUT: api/Leaves/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutLeave(int id, Leave leave)
        {
            if (id != leave.LeaveID)
            {
                return BadRequest();
            }

            _context.Entry(leave).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LeaveExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpPut("Approved/{id}/{approvedbyID}")]
        public async Task<IActionResult> ApproveLeave(int id, int approvedbyID, [FromBody] string rejectionReason = null)
        {
            var leave = await _context.Leaves.FindAsync(id);
            if (leave == null)
            {
                return NotFound();
            }

            leave.IsApproved = true;
            leave.ApprovedbyEmpID = approvedbyID;
            leave.RejectionReason = rejectionReason;

            _context.Entry(leave).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LeaveExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpPut("Reject/{id}/{approvedbyID}")]
        public async Task<IActionResult> RejectLeave(int id, int approvedbyID, [FromBody] string rejectionReason)
        {
            var leave = await _context.Leaves.FindAsync(id);
            if (leave == null)
            {
                return NotFound();
            }

            leave.IsApproved = false;
            leave.ApprovedbyEmpID = approvedbyID;
            leave.RejectionReason = rejectionReason;

            _context.Entry(leave).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LeaveExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }



        [HttpGet("GetLeavesbyEmpID/{id}")]
        public async Task<ActionResult<IEnumerable<object>>> GetLeavesByEmpID(int id)
        {
            var leaves = await _context.Leaves
                .Where(l => l.EmpID == id)
                // First join: Leave.EmpID → Employee (the one who applied)
                .Join(_context.Employees,
                      leave => leave.EmpID,
                      emp => emp.EmpID,
                      (leave, emp) => new
                      {
                          leave,
                          EmployeeName = $"{emp.FirstName} {emp.LastName}"
                      })
                // Left join: Leave.ApprovedbyEmpID → Employee (the approver)
                .GroupJoin(
                      _context.Employees,
                      temp => temp.leave.ApprovedbyEmpID,
                      approver => approver.EmpID,
                      (temp, approvers) => new { temp, approvers })
                .SelectMany(
                      x => x.approvers.DefaultIfEmpty(), // ensures we still get results when approver is null
                      (x, approver) => new
                      {
                          x.temp.leave.LeaveID,
                          x.temp.leave.EmpID,
                          x.temp.EmployeeName,
                          ApproverName = approver != null ? $"{approver.FirstName} {approver.LastName}" : null,
                          x.temp.leave.StartDate,
                          x.temp.leave.EndDate,
                          x.temp.leave.LeaveType,
                          x.temp.leave.LeaveReason,
                          x.temp.leave.IsApproved,
                          x.temp.leave.ApprovedbyEmpID,
                          x.temp.leave.CreatedAt
                      })
                .ToListAsync();

            if (!leaves.Any())
            {
                return NotFound("No leaves found for this employee");
            }

            return Ok(leaves);
        }

        [HttpGet("GetLeavestobeApproved/{id}")]
        public async Task<ActionResult<IEnumerable<Leave>>> GetLeavesToBeApproved(int id)
        {
           

            // Step 1: Get the PositionID of the logged-in user
            var userPositionId = await _context.EmpDeptPositions
                .Where(e => e.EmpID == id)
                .Select(e => e.PositionID)
                .FirstOrDefaultAsync();

            if (userPositionId == 0)
            {
                return NotFound("No position found for the logged-in employee");
            }

            // Step 2: Find all positions that report to the logged-in user's position
            var reportingPositionIds = (await _context.Positions
        .Where(p => p.ReportsTo != null)
        .ToListAsync()) // Load to memory
    .Where(p => p.ReportsTo.Contains(userPositionId)) // LINQ to Objects
    .Select(p => p.PositionID)
    .ToList();

            foreach (var positionId in reportingPositionIds)
            {
                // Ensure the position exists
                Console.WriteLine($"PositionID: {positionId} exists in the database.");
            }

            if (!reportingPositionIds.Any())
            {
                return NotFound("No subordinate positions found for this user");
            }

            // Step 3: Get all EmpIDs that have these reporting positions
            var subordinateEmpIds = await _context.EmpDeptPositions
                .Where(e => reportingPositionIds.Contains(e.PositionID))
                .Select(e => e.EmpID)
                .ToListAsync();

            foreach (var empId in subordinateEmpIds)
            {

                // Ensure the employee exists
                Console.WriteLine($"EmpID: {empId} exists in the database.");
            }

            if (!subordinateEmpIds.Any())
            {
                return NotFound("No employees found under this user's supervision");
            }

            // Step 4: Fetch leaves for those employees where IsApproved is NULL
            var leaves = await _context.Leaves
    .Where(l => subordinateEmpIds.Contains(l.EmpID) && l.IsApproved == null)
    .Join(_context.Employees,
          leave => leave.EmpID,
          emp => emp.EmpID,
          (leave, emp) => new
          {
              leave.LeaveID,
              leave.EmpID,
              EmployeeName = $"{emp.FirstName} {emp.LastName}", // assuming column name is EmpName
              leave.StartDate,
              leave.EndDate,
              leave.LeaveType,
              leave.LeaveReason,
              leave.IsApproved,
              leave.CreatedAt
          })
    .ToListAsync();

            if (!leaves.Any())
            {
                return NotFound("No leaves to approve");
            }

            return Ok(leaves);
        }



        // POST: api/Leaves
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<Leave>> PostLeave(Leave leave)
        {
            _context.Leaves.Add(leave);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetLeave", new { id = leave.LeaveID }, leave);
        }

        // DELETE: api/Leaves/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLeave(int id)
        {
            var leave = await _context.Leaves.FindAsync(id);
            if (leave == null)
            {
                return NotFound();
            }

            _context.Leaves.Remove(leave);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool LeaveExists(int id)
        {
            return _context.Leaves.Any(e => e.LeaveID == id);
        }
    }
}
