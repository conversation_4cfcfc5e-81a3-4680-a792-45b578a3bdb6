import { useState, useEffect } from "react";
import {
  User,
  CalendarCheck,
  DollarSign,
  ListChecks,
  Bell,
  LogOut,
  Menu,
  Clock4,
  <PERSON>hum<PERSON>Up,
  BadgeCheck,
  CheckCircle,
  XCircle,
  Calendar,
} from "lucide-react";
import { Pie, Bar, Line } from "react-chartjs-2";
import {
  Chart,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
} from "chart.js";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import getBaseFileURL from '@/utils/getBaseFileUrl';
import useAuthStore from '@/store/authStore';
import useEmpDataStore from '@/store/empDataStore';
import { FiUser } from "react-icons/fi";


const Home = () => {
  const {
    currentEmployee,
    fetchEmployeeById,
    getEmployeeDisplayName,
    clearEmployeeData,
    loading: empLoading
  } = useEmpDataStore();
  
  const user = useAuthStore((state) => state.user);
  console.log('Current Employee:', currentEmployee);
  console.log('User:', user);
  
  const baseFileURL = getBaseFileURL();

  // All hooks must be called before any early returns
  const [sidebar, setSidebar] = useState(false);
  const navigate = useNavigate();

  // Initialize state with default data that will be updated when currentEmployee is available
  const [announcements, setAnnouncements] = useState([
    { title: "Annual meet on 10th Aug", date: "1 Aug 2025", read: false },
    { title: "Health checkups this Friday", date: "28 Jul 2025", read: true },
    { title: "Payroll processed", date: "31 Jul 2025", read: true },
  ]);
  const [leaveReqs, setLeaveReqs] = useState([
    { id: 1, type: "Casual", days: 3, status: "pending", date: "2 Aug 2025" },
    { id: 2, type: "Sick", days: 1, status: "approved", date: "28 Jul 2025" },
  ]);

  // Fetch employee data when component mounts
  useEffect(() => {
    if (user?.empId && !currentEmployee) {
      console.log('Fetching employee data for empId:', user.empId);
      fetchEmployeeById(user.empId);
    }
  }, [user?.empId, currentEmployee, fetchEmployeeById]);

  Chart.register(
    ArcElement,
    Tooltip,
    Legend,
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement
  );

  // Show loading state while employee data is being fetched
  if (empLoading || !currentEmployee) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-slate-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading employee data...</p>
        </div>
      </div>
    );
  }

  // Mock Data, adapt for your APIs/backend
  const data = {
    user: {
      name: currentEmployee.FirstName + " " + currentEmployee.LastName,
      profilePhoto: `${baseFileURL}${currentEmployee?.ProfilePicture}`,
      employeeId: "EMP000" + currentEmployee.EmpID,
      dept: "Engineering",
    },
    attendance: { present: 12, absent: 2, late: 1, hoursMonth: 112, hoursWeek: 27 },
    leaveBalance: { Paid: 10, Casual: 5, Sick: 2, Unpaid: 1 },
    leavesTaken: 5,
    pendingLeaves: 2,
    lastSalary: { amount: 45000, date: "31 Jul 2025" },
    nextPayday: "31 Aug 2025",
    payHistory: [
      { month: "May", amount: 45000 },
      { month: "Jun", amount: 46000 },
      { month: "Jul", amount: 45000 },
    ],
    tasks: { completed: 18, total: 22, ongoing: 4, pending: 2 },
    performanceRating: 4.2,
    recognitions: 3,
    announcements: [
      { title: "Annual meet on 10th Aug", date: "1 Aug 2025", read: false },
      { title: "Health checkups this Friday", date: "28 Jul 2025", read: true },
      { title: "Payroll processed", date: "31 Jul 2025", read: true },
    ],
    activity: [
      { type: "Leave Approved", timestamp: "31 Jul 10:00" },
      { type: "Checked In", timestamp: "31 Jul 09:04" },
      { type: "Payslip Downloaded", timestamp: "30 Jul 15:22" },
    ],

    leaveRequests: [
      { id: 1, type: "Casual", days: 3, status: "pending", date: "2 Aug 2025" },
      { id: 2, type: "Sick", days: 1, status: "approved", date: "28 Jul 2025" },
    ],
  };

  // Chart Data/Options — adjust to match your needs
  const leavePieData = {
    labels: Object.keys(data.leaveBalance),
    datasets: [
      {
        data: Object.values(data.leaveBalance),
        backgroundColor: ["#8e44ad", "#1abc9c", "#f1c40f", "#e74c3c"],
        borderWidth: 2,
      },
    ],
  };

  const attendancePieData = {
    labels: ["Present", "Absent", "Late"],
    datasets: [
      {
        data: [
          data.attendance.present,
          data.attendance.absent,
          data.attendance.late,
        ],
        backgroundColor: ["#4caf50", "#f44336", "#ff9800"],
        borderWidth: 2,
      },
    ],
  };

  function Tile({ title, value, icon, color, hint }) {
    return (
      <motion.div
        layout
        className="bg-gradient-to-br from-slate-200 via-white to-white rounded-xl shadow flex items-center p-5 gap-4"
        whileHover={{ scale: 1.05 }}
      >
        <div className={`bg-white p-2 rounded-lg shadow-inner ${color} flex items-center justify-center w-12 h-12`}>
          {icon}
        </div>
        <div>
          <div className="font-bold text-xl">{value}</div>
          <div className="text-gray-600 tracking-tight text-sm">{title}</div>
          {hint && <div className="text-xs text-gray-400">{hint}</div>}
        </div>
      </motion.div>
    );
  }


  // Calculate salary growth %
  const salaryCurrent = data.payHistory[data.payHistory.length - 1].amount;
  const salaryPrev = data.payHistory.length > 1 ? data.payHistory[data.payHistory.length - 2].amount : 0;
  const salaryGrowth =
    salaryPrev > 0 ? (((salaryCurrent - salaryPrev) / salaryPrev) * 100).toFixed(1) : null;

  // Announcement count unread
  const unreadCount = announcements.filter((a) => !a.read).length;

  // Handlers
  const toggleAnnouncementRead = (index) => {
    const updated = [...announcements];
    updated[index].read = !updated[index].read;
    setAnnouncements(updated);
  };

  const approveLeave = (id) => {
    setLeaveReqs((prev) =>
      prev.map((req) => (req.id === id ? { ...req, status: "approved" } : req))
    );
  };
  const rejectLeave = (id) => {
    setLeaveReqs((prev) =>
      prev.map((req) => (req.id === id ? { ...req, status: "rejected" } : req))
    );
  };

  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-US", {
    weekday: "long",
    month: "long",
    day: "numeric",
    year: "numeric",
  });

  const handleRequestLeave = () => {
    navigate("/dashboard/leave-management/apply");
  }

  const handleProfileClick = () => {
    navigate("/profile");
  };

  // console.log(data.user.profilePhoto)

  return (
    <div className="flex rounded-lg bg-slate-100 min-h-screen relative">
      <main
        className={`flex-1 transition-all ml-1 duration-300 px-6 py-4`}
      >
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
          <div>
            <h1 className="text-4xl font-extrabold tracking-tight text-gray-800">
              Welcome, {data.user.name}!
            </h1>
            <div className="text-sm text-gray-500">
              {data.user.employeeId} &bull; {data.user.dept}
            </div>
            <div className="mt-1 text-sm text-slate-600 font-medium">{formattedDate}</div>
          </div>
          {/* Profile Photo */}
          {currentEmployee?.ProfilePicture ?  <motion.img
            src={data.user.profilePhoto}
            alt="Profile"
            className="rounded-full w-16 h-16 border-4 border-slate-200 object-cover shadow-md cursor-pointer"
            initial={{ rotate: -6, scale: 0.93 }}
            animate={{ rotate: 0, scale: 1 }}
            transition={{ duration: 0.5 }}
            onClick={handleProfileClick}
          /> : <FiUser className="rounded-full w-16 h-16 border-4 p-2 text-gray-700 border-slate-200 object-cover shadow-md cursor-pointer" onClick={handleProfileClick} />}
         
        </div>

        {/* Top Tiles */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-5 mb-5">
          <Tile
            title="Total Hours (Month)"
            value={data.attendance.hoursMonth + "h"}
            icon={<Clock4 className="text-slate-500" />}
            color="bg-slate-100"
            hint={`Week: ${data.attendance.hoursWeek}h`}
          />
          <Tile
            title="Pending Leaves"
            value={data.pendingLeaves}
            icon={<CalendarCheck className="text-green-600" />}
            color="bg-green-100"
            hint={`${data.leavesTaken} used`}
          />
          {/* <Tile
            title="Recognitions"
            value={data.recognitions}
            icon={<BadgeCheck className="text-amber-400" />}
            color="bg-amber-50"
            hint="Peer or manager kudos"
          /> */}
          <Tile
            title="Performance"
            value={data.performanceRating + " ★"}
            icon={<ThumbsUp className="text-purple-400" />}
            color="bg-purple-50"
            hint="Last review"
          />
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-3 gap-8">
          {/* Attendance Card */}
          <motion.div
            className="bg-white rounded-xl shadow-md p-6 flex flex-col items-center"
            whileHover={{ scale: 1.03 }}
          >
            <div className="flex items-center gap-2 mb-3">
              <CalendarCheck className="text-green-600" size={22} />
              <h3 className="font-semibold text-lg text-gray-800">Attendance</h3>
            </div>
            <Pie data={attendancePieData} className="mb-3 max-w-[180px]" />
            <div className="text-gray-600 text-center text-sm flex flex-wrap justify-center gap-2 font-medium">
              {Object.entries(data.attendance)
                .filter(([k]) => ["present", "absent", "late"].includes(k))
                .map(([k, v]) => (
                  <span key={k} className="mx-1">
                    {k.charAt(0).toUpperCase() + k.slice(1)}: <b>{v}</b>
                  </span>
                ))}
            </div>
            <div className="mt-2 text-xs text-gray-400">
              Month: {data.attendance.hoursMonth}h &bull; Week: {data.attendance.hoursWeek}h
            </div>
          </motion.div>

          {/* Leave Balance Card */}
          <motion.div
            className="bg-white rounded-xl shadow-md p-6 flex flex-col items-center"
            whileHover={{ scale: 1.03 }}
          >
            <div className="flex items-center gap-2 mb-3">
              <User className="text-purple-600" size={22} />
              <h3 className="font-semibold text-lg text-gray-800">Leave Balance</h3>
            </div>
            <Pie data={leavePieData} className="mb-3 max-w-[180px]" />
            <div className="text-gray-600 mb-1 text-sm font-medium">
              {Object.entries(data.leaveBalance).map(([type, val]) => (
                <span key={type} className="mx-1">
                  {type}: <b>{val}</b>
                </span>
              ))}
            </div>
            <div className="text-xs text-gray-400">
              Used: {data.leavesTaken} &bull; Pending: {data.pendingLeaves}
            </div>
            <button className="mt-4 px-3 py-1 bg-slate-600 text-white rounded hover:bg-slate-700 text-sm shadow-md"
              onClick={handleRequestLeave}
            >
              Request Leave
            </button>
          </motion.div>

          {/* Payroll Card */}
          <motion.div
            className="bg-white rounded-xl shadow-md p-6 flex flex-col items-center"
            whileHover={{ scale: 1.03 }}
          >
            <div className="flex items-center gap-2 mb-3">

              <h3 className="font-semibold text-lg text-gray-800">₹ Payroll</h3>
            </div>
            <div className="text-green-800 font-bold mb-1 text-2xl flex items-center gap-2">
              ₹{data.lastSalary.amount}
              {salaryGrowth && (
                <span
                  title={`Monthly growth: ${salaryGrowth}%`}
                  className={`text-sm font-semibold ${salaryGrowth > 0 ? "text-green-600" : "text-red-600"
                    }`}
                >
                  ({salaryGrowth > 0 ? "↑" : "↓"}
                  {Math.abs(salaryGrowth)}%)
                </span>
              )}
            </div>
            <div className="text-gray-500 text-sm">Last: {data.lastSalary.date}</div>
            <div className="text-gray-400 text-xs mb-3">Next: {data.nextPayday}</div>
            <button className="text-emerald-600 rounded border border-emerald-500 px-3 py-1 hover:bg-emerald-50 text-xs shadow-md">
              Download Payslip
            </button>
          </motion.div>
        </div>

        {/* Large Panels */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-5">
          {/* Announcements */}
          <motion.div
            className="bg-white rounded-xl shadow-lg p-6 flex flex-col"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.6 }}
          >
            <div className="flex items-center gap-2 mb-3">
              <Bell className="text-amber-500" size={22} />
              <h3 className="font-semibold text-lg text-gray-800 flex items-center gap-2">
                Announcements & Messages
                {unreadCount > 0 && (
                  <span className="bg-red-500 text-white rounded-full text-xs px-2 py-0.5 ml-1 font-semibold">
                    {unreadCount} New
                  </span>
                )}
              </h3>
            </div>
            <ul className="space-y-3 max-h-60 overflow-y-auto">
              {announcements.slice(0, 7).map((a, i) => (
                <li
                  key={i}
                  className={`flex items-center gap-3 cursor-pointer px-3 py-2 rounded ${a.read ? "bg-gray-100" : "bg-slate-50"
                    } hover:bg-slate-100 transition-colors`}
                  onClick={() => toggleAnnouncementRead(i)}
                  title="Click to toggle read/unread"
                >
                  <span className="text-xs text-gray-400 w-16 flex-shrink-0">{a.date}</span>
                  <span className="flex-1 font-medium">{a.title}</span>
                  {a.read ? (
                    <CheckCircle className="text-green-500" size={18} />
                  ) : (
                    <XCircle className="text-red-400" size={18} />
                  )}
                </li>
              ))}
            </ul>
            <button className="mt-4 self-start text-slate-600 hover:underline text-sm font-medium">
              See All Announcements
            </button>
          </motion.div>

          {/* Recent Activity & Leave Requests */}
          <div className="flex flex-col gap-6">
            {/* Recent Activity */}
            <motion.div
              className="bg-white rounded-xl shadow-lg p-6"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <div className="flex items-center gap-2 mb-3">
                <Clock4 className="text-sky-500" size={22} />
                <h3 className="font-semibold text-lg text-gray-800">Recent Activity</h3>
              </div>
              <ol className="relative border-l border-slate-300 px-3 py-2 max-h-52 overflow-y-auto">
                {data.activity.map((event, idx) => (
                  <li key={idx} className="mb-5 ml-4 last:mb-0">
                    <span className="absolute -left-2 w-4 h-4 bg-slate-400 rounded-full mt-1 border border-white shadow"></span>
                    <time className="block text-xs text-gray-400 mb-1">{event.timestamp}</time>
                    <div className="text-gray-700 font-medium">{event.type}</div>
                  </li>
                ))}
              </ol>
            </motion.div>

            {/* Leave Requests - Demo Interactive Panel */}
            <motion.div
              className="bg-white rounded-xl shadow-lg p-6 flex flex-col"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <div className="flex items-center gap-2 mb-4">
                <Calendar className="text-green-600" size={22} />
                <h3 className="font-semibold text-lg text-gray-800">Leave Requests</h3>
              </div>

              {leaveReqs.length === 0 ? (
                <div className="text-gray-500 text-sm italic">No leave requests</div>
              ) : (
                <ul className="divide-y divide-gray-200 max-h-56 overflow-y-auto">
                  {leaveReqs.map((req) => (
                    <li
                      key={req.id}
                      className="flex items-center justify-between py-3 px-2"
                    >
                      <div className="flex flex-col text-sm">
                        <span className="font-semibold">
                          {req.type} Leave - {req.days} day{req.days > 1 ? "s" : ""}
                        </span>
                        <span className="text-gray-400">{req.date}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        {req.status === "pending" && (
                          <>
                            <button
                              onClick={() => approveLeave(req.id)}
                              className="p-1 rounded bg-green-600 text-white hover:bg-green-700 transition"
                              title="Approve"
                            >
                              <CheckCircle size={18} />
                            </button>
                            <button
                              onClick={() => rejectLeave(req.id)}
                              className="p-1 rounded bg-red-600 text-white hover:bg-red-700 transition"
                              title="Reject"
                            >
                              <XCircle size={18} />
                            </button>
                          </>
                        )}
                        {req.status === "approved" && (
                          <span className="text-green-600 font-semibold">Approved</span>
                        )}
                        {req.status === "rejected" && (
                          <span className="text-red-600 font-semibold">Rejected</span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </motion.div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Sub components for clarity
const MenuItem = ({ icon, label, active }) => (
  <div
    title={label}
    className={`group flex flex-col items-center cursor-pointer select-none ${active
      ? "text-slate-600"
      : "text-gray-400 hover:text-slate-600 transition-colors"
      }`}
  >
    <div className="p-2 rounded-lg group-hover:bg-slate-100">{icon}</div>
    <span className="text-xs mt-1">{label}</span>
  </div>
);


export default Home;
